package com.qmqb.imp.system.service.impl.performance;

import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效反馈校验工具类
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PerformanceFeedbackValidationService {

    /**
     * 绩效等级每月提交限制配置
     */
    private static final Map<String, Integer> LEVEL_MONTHLY_LIMIT = new HashMap<>();

    static {
        // S级：组长每月最多填一次and项目经理每月最多填一次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_S.getCode(), 1);
        // A级：组长每月最多填一次and项目经理每月最多填一次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_A.getCode(), 1);
        // B级：不需要填
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_B.getCode(), 0);
        // C级：组长每月最多填3次and项目经理每月最多填3次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_C.getCode(), 3);
        // D级：组长每月最多填3次and项目经理每月最多填3次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_D.getCode(), 3);
    }

    private final PerformanceFeedbackMainMapper performanceFeedbackMainMapper;
    private final PerformanceFeedbackMapper performanceFeedbackMapper;
    private final ISysUserService sysUserService;

    /**
     * 校验新增绩效反馈数据
     *
     * @param feedbackList 反馈列表
     * @param submitter 提交人
     * @param year 年份
     * @param month 月份
     * @param eventEndTime 事件结束时间（从主表获取）
     */
    public void validateNewPerformanceFeedback(List<PerformanceFeedback> feedbackList, String submitter, Integer year, Integer month, Date eventEndTime) {
        if (feedbackList == null || feedbackList.isEmpty()) {
            return;
        }

        // 1. 校验事件结束时间是否为当月
        validateEventEndTime(eventEndTime);

        // 2. 按等级+二级指标分组校验
        Map<String, List<PerformanceFeedback>> levelSecondaryGroups = feedbackList.stream()
            .collect(Collectors.groupingBy(feedback ->
                feedback.getRecommendedLevel() + "_" + feedback.getSecondaryIndicator()));

        for (Map.Entry<String, List<PerformanceFeedback>> entry : levelSecondaryGroups.entrySet()) {
            String levelSecondaryKey = entry.getKey();
            List<PerformanceFeedback> levelSecondaryFeedbacks = entry.getValue();

            // 从key中提取等级和二级指标
            String[] parts = levelSecondaryKey.split("_", 2);
            String level = parts[0];
            String secondaryIndicator = parts.length > 1 ? parts[1] : null;

            // 不限制技术总监的绩效事件提交次数
            if("lihaiyang".equals(submitter)) {
                return;
            }

            // 校验单个事件中同等级+二级指标人员数量
            validateSingleEventLevelSecondaryCount(level, secondaryIndicator, levelSecondaryFeedbacks);

            // 校验提交人本月该等级+二级指标的提交限制
            validateSubmitterMonthlyLimit(level, secondaryIndicator, submitter, year, month);
        }
    }

    /**
     * 校验项管审核
     *
     * @param mainList 主表列表
     */
    public void validateProjectManagerAudit(List<PerformanceFeedbackMain> mainList,String status,Boolean isFinalAudit) {
        for (PerformanceFeedbackMain main : mainList) {
            // 拒绝状态不需要校验
            if (PerformanceFeedbackAuditStatusEnum.REJECTED.getCode().equals(status)) {
                continue;
            }
            // 权限控制逻辑
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null) {
                throw new ServiceException("当前用户未登录");
            }
            SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
            //去除项目经理、技术总监的审核条数限制
            if (currentUser != null && currentUser.getRoles() != null) {
                return;
            }
            if (currentUser.isJszxAdmin() || currentUser.isProjectManager()) {
                return;
            }
            String submitter = main.getSubmitter();
            Integer year = main.getYear();
            Integer month = main.getMonth();

            // 获取该提交人本月已审核通过的数据
            PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
            bo.setSubmitter(submitter);
            bo.setYear(year);
            bo.setMonth(month);
            bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
            bo.setSecondaryIndicator(main.getSecondaryIndicator());
            if (isFinalAudit) {
                // 如果是最终审核，查询所有已审核数据
                bo.setFinalAudit(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
            } else {
                // 如果是项管审核，查询所有已审核数据
                bo.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
            }
            List<PerformanceFeedbackVo> approvedList = performanceFeedbackMapper.selectVoJoinMainList(bo);

            // 按等级分组统计
            Map<String, Long> levelCountMap = approvedList.stream()
                .collect(Collectors.groupingBy(
                    PerformanceFeedbackVo::getRecommendedLevel,
                    Collectors.counting()
                ));

            PerformanceFeedbackBo feedbackBo = new PerformanceFeedbackBo();
            feedbackBo.setMainFeedbackId(main.getId());
            List<PerformanceFeedbackVo> currentAuditVo = performanceFeedbackMapper.selectVoJoinMainList(feedbackBo);

            // 合并levelCountMap和currentAuditVo，
            for (PerformanceFeedbackVo feedbackVo : currentAuditVo) {
                String level = feedbackVo.getRecommendedLevel();
                levelCountMap.put(level, levelCountMap.getOrDefault(level, 0L) + 1);
            }

            // 已审核数据+本次应审核数据，校验各等级是否超过限制
            for (Map.Entry<String, Long> entry : levelCountMap.entrySet()) {
                String level = entry.getKey();
                Long count = entry.getValue();
                Integer limit = LEVEL_MONTHLY_LIMIT.get(level);

                if (limit != null && count > limit) {
                    String indicatorDesc = main.getSecondaryIndicator() != null ? "(" + main.getSecondaryIndicator() + ")" : "";
                    throw new ServiceException(String.format("%s本月提交%s%s绩效数据已审核通过数量超过限定数量", submitter, level, indicatorDesc));
                }
            }
        }
    }

    /**
     * 校验最终审核
     *
     * @param mainList 主表列表
     */
    public void validateFinalAudit(List<PerformanceFeedbackMain> mainList,String status) {
        // 最终审核的校验逻辑与项管审核相同
        validateProjectManagerAudit(mainList,status,true);
    }

    /**
     * 校验事件结束时间是否为当月
     *
     * @param eventEndTime 事件结束时间
     */
    private void validateEventEndTime(Date eventEndTime) {
        if (eventEndTime == null) {
            return;
        }

        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

        LocalDate eventEndDate = eventEndTime.toInstant()
            .atZone(ZoneId.systemDefault()).toLocalDate();

        if (eventEndDate.getYear() != currentYear || eventEndDate.getMonthValue() != currentMonth) {
            throw new ServiceException("仅允许提交结束时间为当月的数据，请修改事件发生时间");
        }
    }

    /**
     * 校验单个事件中同等级+二级指标人员数量
     *
     * @param level 等级
     * @param secondaryIndicator 二级指标
     * @param levelSecondaryFeedbacks 该等级+二级指标的反馈列表
     */
    private void validateSingleEventLevelSecondaryCount(String level, String secondaryIndicator, List<PerformanceFeedback> levelSecondaryFeedbacks) {
        Integer limit = LEVEL_MONTHLY_LIMIT.get(level);
        if (limit != null && levelSecondaryFeedbacks.size() > limit) {
            String indicatorDesc = secondaryIndicator != null ? "(" + secondaryIndicator + ")" : "";
            throw new ServiceException(String.format("每月%s%s绩效数据仅能填写%d次，请修改后重新提交", level, indicatorDesc, limit));
        }
    }

    /**
     * 校验提交人本月该等级+二级指标的提交限制
     *
     * @param level 等级
     * @param secondaryIndicator 二级指标
     * @param submitter 提交人
     * @param year 年份
     * @param month 月份
     */
    private void validateSubmitterMonthlyLimit(String level, String secondaryIndicator, String submitter, Integer year, Integer month) {
        Integer limit = LEVEL_MONTHLY_LIMIT.get(level);
        if (limit == null || limit == 0) {
            return;
        }

        // 获取提交人本月该等级+二级指标已提交的数据
        List<PerformanceFeedbackVo> submittedList = getSubmittedFeedbackBySubmitterAndLevel(submitter, level, secondaryIndicator, year, month);

        // 统计已审核通过的数量
        long approvedCount = submittedList.stream()
            .filter(main -> PerformanceFeedbackAuditStatusEnum.APPROVED.getCode().equals(main.getFinalAudit()))
            .count();

        // 统计未审核通过的数量
        long unapprovedCount = submittedList.stream()
            .filter(main -> PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode().equals(main.getProjectManagerAuditStatus()))
            .count();

        // 如果已审核通过的数据大于等于限制
        if (approvedCount >= limit) {
            PerformanceIndicatorEnum byCode = PerformanceIndicatorEnum.fromCode(secondaryIndicator);
            throw new ServiceException(String.format("您本月提交的%s%s绩效数据已被审核通过，每月仅能提交%d条，请修改后重新提交", level, byCode.getName(), limit));
        }

        // 如果未审核通过 + 已审核通过的数量已达到限制，不允许再提交
        if (approvedCount + unapprovedCount >= limit + 1) {
            PerformanceIndicatorEnum byCode = PerformanceIndicatorEnum.fromCode(secondaryIndicator);
            throw new ServiceException(String.format("您本月已提交%d条未审核%s%s绩效数据，已超过限定数量，请修改后重新提交", unapprovedCount, level, byCode.getName()));
        }
    }

    /**
     * 获取提交人指定年月指定等级的已提交反馈数据
     * 使用PerformanceFeedbackMapper#selectVoJoinMainList方法
     *
     * @param submitter 提交人
     * @param level 等级
     * @param secondaryIndicator 二级指标
     * @param year 年份
     * @param month 月份
     * @return 反馈主表列表
     */
    private List<PerformanceFeedbackVo> getSubmittedFeedbackBySubmitterAndLevel(String submitter, String level, String secondaryIndicator, Integer year, Integer month) {
        // 构建查询条件
        PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
        bo.setSubmitter(submitter);
        bo.setYear(year);
        bo.setMonth(month);
        bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
        if (level != null) {
            bo.setRecommendedLevel(level);
        }
        if (secondaryIndicator != null) {
            bo.setSecondaryIndicator(secondaryIndicator);
        }
        return performanceFeedbackMapper.selectVoJoinMainList(bo);
    }

}
